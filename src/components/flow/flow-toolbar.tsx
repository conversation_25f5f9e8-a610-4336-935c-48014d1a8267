'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>ip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { ImageIcon, MessageSquareIcon, MicIcon, PlayIcon } from 'lucide-react';
import { useState } from 'react';

interface ToolItem {
  id: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  action?: () => void;
  nodeType?: string;
  isActive?: boolean;
}

const tools: ToolItem[] = [
  {
    id: 'image',
    icon: ImageIcon,
    label: 'Image',
    nodeType: 'image',
  },
  {
    id: 'youtube',
    icon: PlayIcon,
    label: 'YouTube Video',
    nodeType: 'youtube',
  },
  {
    id: 'voice',
    icon: MicIcon,
    label: 'Voice Recorder',
    nodeType: 'voiceRecorder',
  },
  {
    id: 'chat',
    icon: MessageSquareIcon,
    label: 'Chat Interface',
    nodeType: 'chat',
  },
];

interface FlowToolbarProps {
  onAddNode: (type: string, position?: { x: number; y: number }) => void;
}

export function FlowToolbar({ onAddNode }: FlowToolbarProps) {
  const [draggedTool, setDraggedTool] = useState<string | null>(null);

  const handleToolClick = (tool: ToolItem) => {
    if (tool.nodeType) {
      onAddNode(tool.nodeType);
    }
  };

  const handleDragStart = (event: React.DragEvent, tool: ToolItem) => {
    if (tool.nodeType) {
      setDraggedTool(tool.nodeType);
      event.dataTransfer.setData('application/reactflow', tool.nodeType);
      event.dataTransfer.effectAllowed = 'move';
    }
  };

  const handleDragEnd = () => {
    setDraggedTool(null);
  };

  return (
    <div className="absolute top-1/2 left-4 -translate-y-1/2 z-10">
      <div className="bg-background/95 backdrop-blur-sm border border-border rounded-lg p-2 shadow-lg">
        <div className="flex flex-col gap-1">
          {tools.map((tool) => (
            <div key={tool.id}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="sm"
                    variant="ghost"
                    className={`w-10 h-10 p-0 cursor-grab active:cursor-grabbing transition-opacity ${
                      draggedTool === tool.nodeType ? 'opacity-50' : ''
                    }`}
                    onClick={() => handleToolClick(tool)}
                    draggable
                    onDragStart={(e) => handleDragStart(e, tool)}
                    onDragEnd={handleDragEnd}
                  >
                    <tool.icon className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>{tool.label}</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Click to add or drag to canvas
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
