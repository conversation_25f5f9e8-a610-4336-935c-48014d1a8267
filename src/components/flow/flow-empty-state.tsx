'use client';

import {
  FacebookIcon,
  InstagramIcon,
  LinkedInIcon,
  TikTokIcon,
  WebsiteIcon,
} from '@/components/icons/social';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';
import {
  ImageIcon,
  MessageSquareIcon,
  MicIcon,
  PlayIcon,
  PlusIcon,
  UploadIcon,
} from 'lucide-react';

interface FlowEmptyStateProps {
  onAddNode: (type: string, position?: { x: number; y: number }) => void;
}

export function FlowEmptyState({ onAddNode }: FlowEmptyStateProps) {
  const primaryActions = [
    {
      id: 'chat',
      icon: MessageSquareIcon,
      label: 'AI Chat',
      nodeType: 'chat',
      variant: 'default' as const,
    },
    {
      id: 'text',
      icon: PlusIcon,
      label: 'Add Text',
      nodeType: 'chat', // Map to chat for now
      variant: 'outline' as const,
    },
    {
      id: 'voice',
      icon: MicIcon,
      label: 'Record Voice',
      nodeType: 'voiceRecorder',
      variant: 'outline' as const,
    },
    {
      id: 'upload',
      icon: UploadIcon,
      label: 'Upload Docs, Audio and Video',
      nodeType: 'image',
      variant: 'outline' as const,
    },
  ];

  const socialPlatforms = [
    {
      id: 'youtube',
      icon: PlayIcon,
      label: 'YouTube',
      nodeType: 'youtube',
    },
    {
      id: 'instagram',
      icon: InstagramIcon,
      label: 'Instagram',
      nodeType: 'image', // Map to image for now
    },
    {
      id: 'tiktok',
      icon: TikTokIcon,
      label: 'TikTok',
      nodeType: 'image', // Map to image for now
    },
    {
      id: 'linkedin',
      icon: LinkedInIcon,
      label: 'LinkedIn',
      nodeType: 'image', // Map to image for now
    },
    {
      id: 'facebook',
      icon: FacebookIcon,
      label: 'Facebook Ads',
      nodeType: 'image', // Map to image for now
    },
    {
      id: 'website',
      icon: WebsiteIcon,
      label: 'Website',
      nodeType: 'image', // Map to image for now
    },
  ];

  const handleActionClick = (nodeType: string) => {
    // Center the node in the viewport
    const centerPosition = { x: 400, y: 300 };
    onAddNode(nodeType, centerPosition);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="absolute inset-0 flex items-center justify-center pointer-events-none"
    >
      <div className="text-center space-y-8 max-w-2xl mx-auto px-6 pointer-events-auto">
        {/* Main heading */}
        <motion.h1
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1, duration: 0.5 }}
          className="text-2xl font-medium text-foreground"
        >
          Drag and drop files here or click a button to start creating content
        </motion.h1>

        {/* Primary action buttons */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.5 }}
          className="flex flex-wrap justify-center gap-3"
        >
          {primaryActions.map((action) => (
            <Button
              key={action.id}
              variant={action.variant}
              size="lg"
              onClick={() => handleActionClick(action.nodeType)}
              className="flex items-center gap-2 h-12 px-6"
            >
              <action.icon className="h-5 w-5" />
              {action.label}
            </Button>
          ))}
        </motion.div>

        {/* OR separator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="flex items-center gap-4"
        >
          <div className="flex-1 h-px bg-border" />
          <span className="text-sm text-muted-foreground font-medium">OR</span>
          <div className="flex-1 h-px bg-border" />
        </motion.div>

        {/* Secondary instruction */}
        <motion.p
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.5 }}
          className="text-base text-muted-foreground"
        >
          Use Ctrl/Cmd + V to paste social media content and websites
        </motion.p>

        {/* Social media platforms */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.5 }}
          className="flex flex-wrap justify-center gap-4"
        >
          {socialPlatforms.map((platform) => (
            <Button
              key={platform.id}
              variant="ghost"
              size="lg"
              onClick={() => handleActionClick(platform.nodeType)}
              className="flex items-center gap-3 h-14 px-6 border border-border/50 hover:border-border hover:bg-accent/50"
            >
              <platform.icon className="h-6 w-6" />
              <span className="text-sm font-medium">{platform.label}</span>
            </Button>
          ))}
        </motion.div>
      </div>
    </motion.div>
  );
}
