import { CommandIcon } from 'lucide-react';
import Link from 'next/link';

import {
  Sidebar,
  Sidebar<PERSON>ontent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from '@/components/ui/sidebar';

import { SidebarProjects } from './sidebar-projects';
import { SidebarUserDropdown } from './sidebar-user-dropdown';

import { SiteConfig, routes } from '@/configuration';

export function DashboardSidebar({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              size="lg"
              className="hover:bg-transparent"
              asChild
            >
              <Link href={routes.dashboard.Index}>
                <span className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                  <CommandIcon className="size-4" />
                </span>
                <span className="grid flex-1 text-left text-sm leading-tight truncate font-semibold">
                  {SiteConfig.site.name}
                </span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        <SidebarProjects />
      </SidebarContent>

      <SidebarFooter>
        <SidebarUserDropdown />
      </SidebarFooter>

      <SidebarRail />
    </Sidebar>
  );
}
